# Navigation user interface 

The Navigation user interface consists of the Navigation window, AI Navigation overlay, AI Navigation Editor Preferences and several components for building a NavMesh. The NavMesh building components provide you with additional controls that allow you to generate and use NavMeshes at runtime and in the Unity Editor.

| **Topic**             | **Description**         |
| :-------------------- | :----------------------- |
| [**Navigation window**](./NavigationWindow.md) | Define the types of agents and areas in your game world. |
| [**AI Navigation preferences**](./NavEditorPreferences.md) | Customize the navigation debug visualization. |
| [**AI Navigation overlay**](./NavigationOverlay.md) | Display navigation debug visualization. |
| [**NavMesh Agent component**](./NavMeshAgent.md) | Define the characters that you want to navigate the game world. |
| [**NavMesh Surface component**](./NavMeshSurface.md) | Build and enable a NavMesh surface for one type of Agent. |
| [**NavMesh Modifier component**](./NavMeshModifier.md) | Adjust the behavior of a GameObject when the NavMesh is baked at runtime. |
| [**NavMesh Modifier Volume component**](./NavMeshModifierVolume.md) | Control the generation of NavMesh area types based on volume. |
| [**NavMesh Obstacle component**](./NavMeshObstacle.md) | Define moving obstacles that NavMesh Agents avoid as they navigate your game world. |
| [**NavMesh Link component**](./NavMeshLink.md) | Connect the NavMesh surfaces for each type of agent. |
