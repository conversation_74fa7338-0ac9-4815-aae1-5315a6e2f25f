%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 15003, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AssetVersion: 1
  m_BuildTarget: -2
  m_Subtarget: 0
  m_PlatformId: 00000000000000000000000000000000
  m_PlatformBuildProfile:
    rid: 4713335073671217156
  m_OverrideGlobalSceneList: 0
  m_Scenes: []
  m_ScriptingDefines: []
  m_PlayerSettingsYaml:
    m_Settings: []
  references:
    version: 2
    RefIds:
    - rid: 4713335073671217156
      type: {class: SharedPlatformSettings, ns: UnityEditor.Build.Profile, asm: UnityEditor.CoreModule}
      data:
        m_Development: 0
        m_ConnectProfiler: 0
        m_BuildWithDeepProfilingSupport: 0
        m_AllowDebugging: 0
        m_WaitForManagedDebugger: 0
        m_ManagedDebuggerFixedPort: 0
        m_ExplicitNullChecks: 0
        m_ExplicitDivideByZeroChecks: 0
        m_ExplicitArrayBoundsChecks: 0
        m_CompressionType: -1
        m_InstallInBuildFolder: 0
        m_WindowsDevicePortalAddress: 
        m_WindowsDevicePortalUsername: 
        m_ForceInstallation: 0
        m_iOSXcodeBuildConfig: 1
        m_SymlinkSources: 0
        m_PreferredXcode: 
        m_SymlinkTrampoline: 0
        m_RemoteDeviceInfo: 0
        m_RemoteDeviceAddress: 
        m_RemoteDeviceUsername: 
        m_RemoteDeviceExports: 
        m_PathOnRemoteDevice: 
