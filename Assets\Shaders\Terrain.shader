Shader "Custom/Terrain/GrassCompatibleURP"
{
    Properties
    {
        [Header(Terrain Textures)]
        // Terrain textures
        _Control("Control (RGBA)", 2D) = "red" {}
        _Splat3("Layer 3 (A)", 2D) = "grey" {}
        _Splat2("Layer 2 (B)", 2D) = "grey" {}
        _Splat1("Layer 1 (G)", 2D) = "grey" {}
        _Splat0("Layer 0 (R)", 2D) = "grey" {}

        [Header(Grass Color Matching System)]
        // Grass color matching system
        _TerrainMask("Terrain Mask (Grayscale)", 2D) = "white" {}
        _DarkColor("Dark Color", Color) = (0.2, 0.3, 0.1, 1)
        _MidColor("Mid Color", Color) = (0.4, 0.6, 0.2, 1)
        _LightColor("Light Color", Color) = (0.7, 0.9, 0.3, 1)
        _ColorGradientOffset("Gradient Offset", Range(-1, 1)) = 0.0
        _ColorBlend("Color Blend Strength", Range(0, 1)) = 0.8
        _Brightness("Brightness", Range(0.5, 10)) = 1.0

        [Header(Fallback)]
        // Fallback
        _MainTex("BaseMap (RGB)", 2D) = "grey" {}
        _BaseColor("Main Color", Color) = (1,1,1,1)
    }

    SubShader
    {
        Tags { "RenderType"="Opaque" "Queue"="Geometry" "RenderPipeline" = "UniversalPipeline" "TerrainCompatible" = "True" }
        LOD 200

        Pass
        {
            Name "ForwardLit"
            Tags { "LightMode" = "UniversalForward" }

            HLSLPROGRAM
            #pragma vertex Vertex
            #pragma fragment Fragment
            #pragma target 3.0

            // URP shadow keywords for stylized shadows
            #if UNITY_VERSION >= 202120
                #pragma multi_compile _ _MAIN_LIGHT_SHADOWS _MAIN_LIGHT_SHADOWS_CASCADE
            #else
                #pragma multi_compile _ _MAIN_LIGHT_SHADOWS
                #pragma multi_compile _ _MAIN_LIGHT_SHADOWS_CASCADE
            #endif
            #pragma multi_compile_fragment _ _SHADOWS_SOFT
            #pragma multi_compile_fog

            // Include our code file
            #include "TerrainForwardLitPass.hlsl"
            ENDHLSL
        }

        // Shadow caster pass for URP
        Pass
        {
            Name "ShadowCaster"
            Tags { "LightMode" = "ShadowCaster" }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull Back

            HLSLPROGRAM
            #pragma vertex ShadowPassVertex
            #pragma fragment ShadowPassFragment
            #pragma target 3.0

            // Shadow caster keywords
            #pragma multi_compile_vertex _ _CASTING_PUNCTUAL_LIGHT_SHADOW

            #include "Packages/com.unity.render-pipelines.universal/Shaders/LitInput.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/Shaders/ShadowCasterPass.hlsl"
            ENDHLSL
        }
    }
    FallBack "Hidden/Universal Render Pipeline/FallbackError"
}