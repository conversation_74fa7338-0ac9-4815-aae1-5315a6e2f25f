// URP Terrain Shader with Grass Color Compatibility
// Based on the MyLit reference shader structure

// Pull in URP library functions
#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"

// Terrain textures
TEXTURE2D(_Control); SAMPLER(sampler_Control);
TEXTURE2D(_Splat0); SAMPLER(sampler_Splat0);
TEXTURE2D(_Splat1); SAMPLER(sampler_Splat1);
TEXTURE2D(_Splat2); SAMPLER(sampler_Splat2);
TEXTURE2D(_Splat3); SAMPLER(sampler_Splat3);
TEXTURE2D(_TerrainMask); SAMPLER(sampler_TerrainMask);
TEXTURE2D(_MainTex); SAMPLER(sampler_MainTex);

// Texture tiling and offset properties (automatically set by Unity)
float4 _Control_ST;
float4 _Splat0_ST, _Splat1_ST, _Splat2_ST, _Splat3_ST;
float4 _TerrainMask_ST;
float4 _MainTex_ST;

// Grass color properties
float4 _DarkColor;
float4 _MidColor;
float4 _LightColor;
float _ColorGradientOffset;
float _ColorBlend;
float _Brightness;
float4 _BaseColor;

// Input attributes from the mesh
struct Attributes {
    float3 positionOS : POSITION;
    float3 normalOS : NORMAL;
    float2 uv : TEXCOORD0;
};

// Output from vertex function, input to fragment function
struct Interpolators {
    float4 positionCS : SV_POSITION;
    float2 uv : TEXCOORD0;
    float3 positionWS : TEXCOORD1;
    float3 normalWS : TEXCOORD2;
    float fogCoord : TEXCOORD3;
};

// Vertex function
Interpolators Vertex(Attributes input) {
    Interpolators output;

    // Transform positions
    VertexPositionInputs positionInputs = GetVertexPositionInputs(input.positionOS);
    VertexNormalInputs normalInputs = GetVertexNormalInputs(input.normalOS);

    output.positionCS = positionInputs.positionCS;
    output.positionWS = positionInputs.positionWS;
    output.normalWS = normalInputs.normalWS;
    output.uv = input.uv;
    
    // Fog
    output.fogCoord = ComputeFogFactor(positionInputs.positionCS.z);

    return output;
}

// Fragment function
float4 Fragment(Interpolators input) : SV_TARGET {
    float2 uv = input.uv;
    
    // Sample control texture to determine terrain layer blending
    float4 control = SAMPLE_TEXTURE2D(_Control, sampler_Control, uv);
    
    // Sample all terrain layer textures
    float4 splat0 = SAMPLE_TEXTURE2D(_Splat0, sampler_Splat0, uv * _Splat0_ST.xy + _Splat0_ST.zw);
    float4 splat1 = SAMPLE_TEXTURE2D(_Splat1, sampler_Splat1, uv * _Splat1_ST.xy + _Splat1_ST.zw);
    float4 splat2 = SAMPLE_TEXTURE2D(_Splat2, sampler_Splat2, uv * _Splat2_ST.xy + _Splat2_ST.zw);
    float4 splat3 = SAMPLE_TEXTURE2D(_Splat3, sampler_Splat3, uv * _Splat3_ST.xy + _Splat3_ST.zw);
    
    // Blend terrain textures based on control map
    float4 baseColor = splat0 * control.r + splat1 * control.g + splat2 * control.b + splat3 * control.a;
    
    // Sample terrain mask for grass color compatibility
    float terrainMaskValue = SAMPLE_TEXTURE2D(_TerrainMask, sampler_TerrainMask, uv * _TerrainMask_ST.xy + _TerrainMask_ST.zw).r;
    
    // Apply gradient offset (same logic as grass shader)
    terrainMaskValue = saturate(terrainMaskValue + _ColorGradientOffset);
    
    // Generate grass-compatible color using same logic as grass shader
    float4 grassCompatibleColor;
    if (terrainMaskValue < 0.5)
    {
        // Blend between DarkColor and MidColor for values 0.0 to 0.5
        grassCompatibleColor = lerp(_DarkColor, _MidColor, terrainMaskValue * 2.0);
    }
    else
    {
        // Blend between MidColor and LightColor for values 0.5 to 1.0
        grassCompatibleColor = lerp(_MidColor, _LightColor, (terrainMaskValue - 0.5) * 2.0);
    }
    
    // Blend terrain texture with grass-compatible color
    float4 finalColor = lerp(baseColor, baseColor * grassCompatibleColor, _ColorBlend);
    finalColor.rgb *= _Brightness;
    
    return finalColor;
}
