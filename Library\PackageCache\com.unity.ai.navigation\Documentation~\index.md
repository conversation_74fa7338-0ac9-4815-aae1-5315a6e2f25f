# AI Navigation

The navigation system allows you to create characters that can intelligently move around the game world. These characters use navigation meshes that are created automatically from your [**Scene**][1] geometry. Dynamic obstacles allow you to alter the navigation of the characters at runtime, while NavMesh links let you build specific actions like opening doors or jumping over gaps or down from a ledge. This section describes Unity's navigation and pathfinding systems in detail.

The following table describes the main topics of the AI Navigation package documentation.

| **Topic**                                      | **Description**                                             |
|:-----------------------------------------------|:------------------------------------------------------------|
| [**What's new**](./whats-new.md) | See what's changed in the latest version of the AI Navigation package. |
| [**Upgrade**](./UpgradeGuide.md) | Convert your projects to work with the new navigation system. |
| [**Navigation System**](./NavigationSystem.md)      | Understand the key concepts necessary to use AI Navigation in Unity. |
| [**Navigation Overview**](./NavigationOverview.md)  | Create NavMeshes, agents, links, and obstacles with this package. |
| [**Navigation Interface**](./Reference.md)          | Learn about the interface of the Navigation components in this package. |
| [**Samples**](./Samples.md)                         | Learn about the sample projects included with this package. |
| [**Glossary**](./Glossary.md)                       | View AI Navigation terminology definitions. |

## Additional resources
- [A guide on using the new AI Navigation package in Unity 2022 LTS and above](https://discussions.unity.com/t/a-guide-on-using-the-new-ai-navigation-package-in-unity-2022-lts-and-above)
- [Navigation tutorials](http://unity3d.com/learn/tutorials/topics/navigation)
    - [Getting Started with AI Pathfinding](https://learn.unity.com/project/beginner-ai-pathfinding)
    - [Navigation Meshes](https://learn.unity.com/project/navigation-meshes)
    - [Working with NavMesh Agents](https://learn.unity.com/tutorial/working-with-navmesh-agents)
- [3D Game Kit](https://assetstore.unity.com/packages/templates/tutorials/unity-learn-3d-game-kit-115747) and [3D Game Kit Lite](https://assetstore.unity.com/packages/templates/tutorials/3d-game-kit-lite-135162) - Sample projects that include navigation
- [The Explorer: 3D Game Kit](https://learn.unity.com/project/3d-game-kit?uv=2020.3) - Tutorial about the sample project
    - [Enemies in the 3D Game Kit](https://learn.unity.com/tutorial/quick-start?uv=2020.3&projectId=5c514897edbc2a001fd5bdd0#5c7f8528edbc2a002053b746)
    - [A Deeper Look at Enemies](https://learn.unity.com/tutorial/3d-game-kit-walkthrough?uv=2020.3&projectId=5c514897edbc2a001fd5bdd0#5c7f8528edbc2a002053b753)
    - [Decorating in 3D Game Kit](https://learn.unity.com/tutorial/quick-start?uv=2020.3&projectId=5c514897edbc2a001fd5bdd0#5c7f8528edbc2a002053b746)
- [Unity Discussions](https://discussions.unity.com/tag/navigation) - Navigation topics on the Unity forums
- [Unity Knowledge Base](https://support.unity3d.com/hc/en-us) 

[1]: ./Glossary.md#scene "A Scene contains the environments and menus of your game. Think of each unique Scene file as a unique level. In each Scene, you place your environments, obstacles, and decorations, essentially designing and building your game in pieces."
