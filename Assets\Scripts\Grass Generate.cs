using UnityEngine;
using UnityEngine.Rendering;
using System.Collections.Generic;

public class GrassFieldRenderer : MonoBehaviour
{
    public Terrain terrain;
    public Mesh grassMesh;
    public Material grassMaterial;
    [Range(1000, 500000)]
    public int density = 200_000;
    public float maxRaycastHeight = 200f;
    public float minScale = 0.8f;
    public float maxScale = 1.2f;
    
    [Header("Grass Control")]
    public int grassLayerIndex = 0; // Which terrain layer controls grass growth
    [Range(0f, 1f)]
    public float minGrassThreshold = 0.1f; // Minimum layer strength to spawn grass
    [Range(0f, 1f)]
    public float maxGrassThreshold = 1f; // Maximum layer strength for full density
    
    [Header("Raycast Settings")]
    public LayerMask terrainLayerMask = 1; // Layer mask for terrain (set to terrain layer)
    public bool useTerrainSampling = true; // Alternative method using terrain height sampling
    
    ComputeBuffer positionBuffer, scaleBuffer, argsBuffer;
    Bounds terrainBounds;
    uint[] args = new uint[5];
    
    void Start()
    {
        terrainBounds = terrain.terrainData.bounds;
        terrainBounds.center += terrain.transform.position;
        
        // Get terrain data for layer sampling
        TerrainData terrainData = terrain.terrainData;
        int alphamapWidth = terrainData.alphamapWidth;
        int alphamapHeight = terrainData.alphamapHeight;
        float[,,] alphamaps = terrainData.GetAlphamaps(0, 0, alphamapWidth, alphamapHeight);
        
        // Generate positions, rotations, and scales
        List<Vector3> validPositions = new List<Vector3>();
        List<Vector4> validRotations = new List<Vector4>();
        List<float> validScales = new List<float>();
        
        for(int i = 0; i < density; i++)
        {
            float x = Random.Range(0f, terrain.terrainData.size.x);
            float z = Random.Range(0f, terrain.terrainData.size.z);
            
            // Convert world position to alphamap coordinates
            int alphaX = Mathf.FloorToInt((x / terrainData.size.x) * alphamapWidth);
            int alphaZ = Mathf.FloorToInt((z / terrainData.size.z) * alphamapHeight);
            alphaX = Mathf.Clamp(alphaX, 0, alphamapWidth - 1);
            alphaZ = Mathf.Clamp(alphaZ, 0, alphamapHeight - 1);
            
            // Check if grass layer index is valid
            if(grassLayerIndex >= terrainData.alphamapLayers)
            {
                Debug.LogWarning($"Grass layer index {grassLayerIndex} is out of range. Terrain has {terrainData.alphamapLayers} layers.");
                continue;
            }
            
            // Get layer strength at this position
            float layerStrength = alphamaps[alphaZ, alphaX, grassLayerIndex];
            
            // Skip if layer strength is below threshold
            if(layerStrength < minGrassThreshold)
                continue;
            
            // Use layer strength to determine spawn probability
            float spawnProbability = Mathf.InverseLerp(minGrassThreshold, maxGrassThreshold, layerStrength);
            if(Random.Range(0f, 1f) > spawnProbability)
                continue;
            
            Vector3 grassPosition;
            Vector3 surfaceNormal;
            
            if(useTerrainSampling)
            {
                // Method 1: Use terrain height sampling (recommended - no raycast needed)
                float terrainHeight = terrain.SampleHeight(terrain.transform.position + new Vector3(x, 0, z));
                grassPosition = terrain.transform.position + new Vector3(x, terrainHeight + 0.01f, z);
                
                // Calculate normal using terrain data
                surfaceNormal = terrainData.GetInterpolatedNormal(x / terrainData.size.x, z / terrainData.size.z);
            }
            else
            {
                // Method 2: Raycast with terrain layer mask
                Vector3 origin = terrain.transform.position + new Vector3(x, maxRaycastHeight, z);
                
                if(Physics.Raycast(origin, Vector3.down, out RaycastHit hit, maxRaycastHeight * 2f, terrainLayerMask))
                {
                    // Only accept hits on terrain
                    if(hit.collider.GetComponent<Terrain>() != null)
                    {
                        grassPosition = hit.point + hit.normal * 0.01f;
                        surfaceNormal = hit.normal;
                    }
                    else
                    {
                        // If raycast hit something else, skip this position
                        continue;
                    }
                }
                else
                {
                    // No terrain hit, skip this position
                    continue;
                }
            }
            
            validPositions.Add(grassPosition);
            
            // Calculate rotation to align with terrain normal
            Vector3 up = surfaceNormal;
            Vector3 forward = Vector3.Cross(up, Vector3.right);
            if(forward.magnitude < 0.1f) forward = Vector3.Cross(up, Vector3.forward);
            forward.Normalize();
            
            Quaternion terrainRotation = Quaternion.LookRotation(forward, up);
            
            // Add random Y rotation for variation
            float randomYRotation = Random.Range(0f, 360f);
            Quaternion finalRotation = terrainRotation * Quaternion.Euler(0, randomYRotation, 0);
            
            validRotations.Add(new Vector4(finalRotation.x, finalRotation.y, finalRotation.z, finalRotation.w));
            
            // Random scale, potentially influenced by layer strength
            float baseScale = Random.Range(minScale, maxScale);
            float layerInfluencedScale = baseScale * Mathf.Lerp(0.7f, 1.3f, layerStrength);
            validScales.Add(layerInfluencedScale);
        }
        
        // Update actual grass count based on valid positions
        int actualGrassCount = validPositions.Count;
        Debug.Log($"Generated {actualGrassCount} grass instances out of {density} attempts");
        
        if(actualGrassCount == 0)
        {
            Debug.LogWarning("No grass instances generated. Check grass layer settings and terrain paint.");
            return;
        }
        
        // Create GPU buffers with actual count
        positionBuffer = new ComputeBuffer(actualGrassCount, sizeof(float) * 3);
        positionBuffer.SetData(validPositions.ToArray());
        
        scaleBuffer = new ComputeBuffer(actualGrassCount, sizeof(float));
        scaleBuffer.SetData(validScales.ToArray());
        
        grassMaterial.SetBuffer("_PositionBuffer", positionBuffer);
        grassMaterial.SetBuffer("_ScaleBuffer", scaleBuffer);
        
        // Pass terrain data to shader for texture sampling
        grassMaterial.SetVector("_TerrainPosition", terrain.transform.position);
        grassMaterial.SetVector("_TerrainSize", terrainData.size);
        
        // Set the terrain layer texture (you need to assign this in inspector or get it from terrain)
        if(grassLayerIndex < terrainData.terrainLayers.Length)
        {
            TerrainLayer layer = terrainData.terrainLayers[grassLayerIndex];
            if(layer != null && layer.diffuseTexture != null)
            {
                grassMaterial.SetTexture("_TerrainTex", layer.diffuseTexture);
                grassMaterial.SetVector("_TerrainTex_ST", new Vector4(layer.tileSize.x, layer.tileSize.y, layer.tileOffset.x, layer.tileOffset.y));
            }
        }
        
        args[0] = (uint)grassMesh.GetIndexCount(0);
        args[1] = (uint)actualGrassCount;
        args[2] = (uint)grassMesh.GetIndexStart(0);
        args[3] = (uint)grassMesh.GetBaseVertex(0);
        args[4] = 0;
        
        argsBuffer = new ComputeBuffer(1, args.Length * sizeof(uint), ComputeBufferType.IndirectArguments);
        argsBuffer.SetData(args);
    }
    
    void Update()
    {
        Graphics.DrawMeshInstancedIndirect(grassMesh, 0, grassMaterial, terrainBounds, argsBuffer, castShadows: ShadowCastingMode.Off);
    }
    
    void OnDisable()
    {
        positionBuffer?.Release();
        scaleBuffer?.Release();
        argsBuffer?.Release();
    }
}