# NavMesh Building Components

The NavMesh-building components provide you with controls that allow you to automatically generate and use NavMeshes at runtime and in the Unity Editor.

Here we introduce four high level components for the navigation system:

* [NavMesh Surface](./NavMeshSurface.md) - Use for building and enabling a NavMesh surface for one type of Agent.
* [NavMesh Modifier](./NavMeshModifier.md) - Use for affecting the NavMesh generation of NavMesh area types based on the transform hierarchy.
* [NavMesh Modifier Volume](./NavMeshModifierVolume.md) - Use for affecting the NavMesh generation of NavMesh area types based on volume.
* [NavMesh Link](./NavMeshLink.md) - Use for connecting the same or different NavMesh surfaces for one type of Agent.
