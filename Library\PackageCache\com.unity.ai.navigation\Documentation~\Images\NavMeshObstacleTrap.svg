<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="285px" height="332px" viewBox="0 0 285 332" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:sketch="http://www.bohemiancoding.com/sketch/ns">
    <title>navmeshobstacle_trap</title>
    <description>Created with Sketch (http://www.bohemiancoding.com/sketch)</description>
    <defs></defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" sketch:type="MSPage">
        <g id="navmeshobstacle_trap" sketch:type="MSArtboardGroup">
            <text id="If-carving-is-not-en" fill="#000000" sketch:type="MSTextLayer" font-family="Helvetica" font-size="12" font-weight="normal">
                <tspan x="136" y="292">If carving is not enabled,</tspan>
                <tspan x="136" y="306">the agent can get stuck</tspan>
                <tspan x="136" y="320">in cluttered environments.</tspan>
            </text>
            <g id="Group" sketch:type="MSLayerGroup" transform="translate(15.000000, 14.000000)">
                <path d="M130.581081,28.4211409 L69.4189189,278.578859" id="Line" stroke="#D52120" stroke-width="2" stroke-linecap="square" sketch:type="MSShapeGroup"></path>
                <text id="When-a-moving-obstac" fill="#FFFFFF" sketch:type="MSTextLayer" font-family="Helvetica" font-size="7.48250729" font-weight="normal">
                    <tspan x="131" y="140">When a moving obstacle</tspan>
                    <tspan x="131" y="150">becomes stationary,</tspan>
                    <tspan x="131" y="160" font-weight="bold">carving</tspan>
                    <tspan x="157.616243" y="160"> should be turned</tspan>
                    <tspan x="131" y="170">on so that the agent can</tspan>
                    <tspan x="131" y="180">plan route around the</tspan>
                    <tspan x="131" y="190">location blocked by the</tspan>
                    <tspan x="131" y="200">obstacle.</tspan>
                </text>
                <g transform="translate(0.000000, 78.000000)" stroke="#979797" stroke-width="2" fill="#D8D8D8" sketch:type="MSShapeGroup">
                    <rect id="Rectangle-27" transform="translate(215.000000, 55.500000) rotate(-267.000000) translate(-215.000000, -55.500000) " x="190" y="30" width="50" height="51"></rect>
                    <rect id="Rectangle-27" transform="translate(142.000000, 130.500000) rotate(-267.000000) translate(-142.000000, -130.500000) " x="117" y="105" width="50" height="51"></rect>
                    <rect id="Rectangle-27-copy" transform="translate(170.000000, 87.500000) rotate(70.000000) translate(-170.000000, -87.500000) " x="145" y="62" width="50" height="51"></rect>
                    <rect id="Rectangle-27-copy-2" transform="translate(30.000000, 30.500000) rotate(-283.000000) translate(-30.000000, -30.500000) " x="5" y="5" width="50" height="51"></rect>
                    <rect id="Rectangle-27-copy-2" transform="translate(92.000000, 109.500000) rotate(-283.000000) translate(-92.000000, -109.500000) " x="67" y="84" width="50" height="51"></rect>
                    <rect id="Rectangle-27-copy-3" transform="translate(44.000000, 81.500000) rotate(-238.000000) translate(-44.000000, -81.500000) " x="19" y="56" width="50" height="51"></rect>
                </g>
                <circle id="Oval-10" stroke="#0AB8FD" fill="#98E0FE" sketch:type="MSShapeGroup" cx="131.5" cy="25.5" r="25.5"></circle>
                <path d="M131.571429,26.4210526 L126.428571,105.578947" id="Line" stroke="#FF481B" stroke-width="2" stroke-linecap="square" fill="#FF481B" sketch:type="MSShapeGroup"></path>
                <path id="Line-decoration-1" d="M126.449628,105.254847 C127.742487,101.550874 128.850653,98.3760406 130.143512,94.6720677 C128.04793,94.5359187 126.251717,94.4192195 124.156135,94.2830704 C124.958858,98.1231923 125.646906,101.414725 126.449628,105.254847 C126.449628,105.254847 126.449628,105.254847 126.449628,105.254847 Z" stroke="#FF481B" stroke-width="2" stroke-linecap="square" fill="#FF481B"></path>
            </g>
        </g>
    </g>
</svg>