{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 17180, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 17180, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 17180, "tid": 1450, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 17180, "tid": 1450, "ts": 1752722600552606, "dur": 1457, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 17180, "tid": 1450, "ts": 1752722600560302, "dur": 1422, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 17180, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 17180, "tid": 1, "ts": 1752722600075387, "dur": 5966, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 17180, "tid": 1, "ts": 1752722600081358, "dur": 67755, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 17180, "tid": 1, "ts": 1752722600149123, "dur": 39016, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 17180, "tid": 1450, "ts": 1752722600561732, "dur": 34, "ph": "X", "name": "", "args": {}}, {"pid": 17180, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600073316, "dur": 8145, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600081463, "dur": 458956, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600082434, "dur": 2741, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600085184, "dur": 1665, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600086852, "dur": 992, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600087848, "dur": 18, "ph": "X", "name": "ProcessMessages 20519", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600087867, "dur": 187, "ph": "X", "name": "ReadAsync 20519", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088058, "dur": 7, "ph": "X", "name": "ProcessMessages 9118", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088067, "dur": 63, "ph": "X", "name": "ReadAsync 9118", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088133, "dur": 1, "ph": "X", "name": "ProcessMessages 1030", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088136, "dur": 54, "ph": "X", "name": "ReadAsync 1030", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088193, "dur": 1, "ph": "X", "name": "ProcessMessages 839", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088194, "dur": 46, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088243, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088245, "dur": 41, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088288, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088290, "dur": 42, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088335, "dur": 36, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088379, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088381, "dur": 38, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088421, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088423, "dur": 34, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088459, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088461, "dur": 36, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088499, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088501, "dur": 41, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088544, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088546, "dur": 36, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088584, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088586, "dur": 38, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088627, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088628, "dur": 30, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088662, "dur": 35, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088699, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088700, "dur": 40, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088744, "dur": 37, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088783, "dur": 2, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088786, "dur": 35, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088824, "dur": 33, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088859, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088860, "dur": 35, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088899, "dur": 40, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088941, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088943, "dur": 38, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088982, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600088984, "dur": 37, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089026, "dur": 39, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089067, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089068, "dur": 35, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089107, "dur": 37, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089146, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089148, "dur": 42, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089193, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089194, "dur": 37, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089233, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089235, "dur": 39, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089276, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089278, "dur": 37, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089319, "dur": 38, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089358, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089360, "dur": 41, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089403, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089405, "dur": 44, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089451, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089453, "dur": 29, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089486, "dur": 37, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089527, "dur": 38, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089566, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089568, "dur": 35, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089606, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089607, "dur": 35, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089644, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089646, "dur": 31, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089681, "dur": 40, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089724, "dur": 37, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089765, "dur": 36, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089803, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089805, "dur": 40, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089848, "dur": 32, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089883, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089884, "dur": 32, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089920, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089959, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600089961, "dur": 35, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090000, "dur": 41, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090043, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090045, "dur": 44, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090091, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090093, "dur": 30, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090127, "dur": 38, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090167, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090168, "dur": 37, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090208, "dur": 35, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090247, "dur": 32, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090282, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090284, "dur": 33, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090319, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090320, "dur": 73, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090397, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090436, "dur": 25, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090463, "dur": 27, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090492, "dur": 1, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090493, "dur": 36, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090532, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090533, "dur": 36, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090573, "dur": 30, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090606, "dur": 159, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090768, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090808, "dur": 34, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090845, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090847, "dur": 36, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090886, "dur": 33, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090922, "dur": 31, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090956, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090957, "dur": 31, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600090991, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091030, "dur": 35, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091068, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091070, "dur": 35, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091107, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091109, "dur": 36, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091147, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091149, "dur": 33, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091184, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091185, "dur": 27, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091216, "dur": 34, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091253, "dur": 44, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091301, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091303, "dur": 40, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091346, "dur": 36, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091384, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091385, "dur": 35, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091424, "dur": 28, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091455, "dur": 30, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091490, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091529, "dur": 36, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091568, "dur": 34, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091604, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091606, "dur": 36, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091645, "dur": 32, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091679, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091680, "dur": 32, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091715, "dur": 35, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091752, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091754, "dur": 34, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091791, "dur": 2, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091793, "dur": 35, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091830, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091832, "dur": 32, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091867, "dur": 30, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091900, "dur": 35, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091939, "dur": 36, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600091978, "dur": 34, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092014, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092015, "dur": 37, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092056, "dur": 31, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092088, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092090, "dur": 35, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092129, "dur": 35, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092167, "dur": 34, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092205, "dur": 28, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092236, "dur": 35, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092273, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092275, "dur": 29, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092308, "dur": 31, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092341, "dur": 36, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092380, "dur": 35, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092417, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092419, "dur": 33, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092455, "dur": 32, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092491, "dur": 26, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092520, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092558, "dur": 34, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092596, "dur": 30, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092629, "dur": 34, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092665, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092666, "dur": 33, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092703, "dur": 29, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092735, "dur": 32, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092770, "dur": 32, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092806, "dur": 34, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092844, "dur": 34, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092880, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092882, "dur": 34, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092919, "dur": 28, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092951, "dur": 30, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600092984, "dur": 32, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093020, "dur": 34, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093057, "dur": 33, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093091, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093093, "dur": 34, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093130, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093131, "dur": 30, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093165, "dur": 30, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093199, "dur": 36, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093238, "dur": 32, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093272, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093273, "dur": 34, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093310, "dur": 33, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093347, "dur": 28, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093378, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093412, "dur": 32, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093447, "dur": 34, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093485, "dur": 34, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093522, "dur": 33, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093558, "dur": 30, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093591, "dur": 30, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093625, "dur": 34, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093662, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093665, "dur": 36, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093703, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093705, "dur": 34, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093742, "dur": 34, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093780, "dur": 26, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093810, "dur": 34, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093846, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093847, "dur": 30, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093880, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093881, "dur": 36, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093921, "dur": 33, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093956, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093958, "dur": 34, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093994, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600093996, "dur": 28, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094027, "dur": 30, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094060, "dur": 36, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094100, "dur": 34, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094136, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094137, "dur": 36, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094175, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094176, "dur": 34, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094214, "dur": 26, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094244, "dur": 34, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094281, "dur": 30, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094316, "dur": 36, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094356, "dur": 35, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094392, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094394, "dur": 30, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094427, "dur": 30, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094461, "dur": 33, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094498, "dur": 34, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094535, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094536, "dur": 33, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094571, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094572, "dur": 33, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094609, "dur": 33, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094643, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094645, "dur": 28, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094676, "dur": 29, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094709, "dur": 35, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094746, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094748, "dur": 33, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094783, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094784, "dur": 32, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094819, "dur": 33, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094856, "dur": 29, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094888, "dur": 1, "ph": "X", "name": "ProcessMessages 201", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094890, "dur": 46, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094940, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094943, "dur": 44, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094990, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600094992, "dur": 38, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095032, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095033, "dur": 36, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095072, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095073, "dur": 27, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095104, "dur": 40, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095148, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095188, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095189, "dur": 34, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095225, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095227, "dur": 44, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095275, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095277, "dur": 51, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095332, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095335, "dur": 31, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095370, "dur": 29, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095403, "dur": 33, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095438, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095440, "dur": 35, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095478, "dur": 35, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095515, "dur": 2, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095518, "dur": 42, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095563, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095566, "dur": 38, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095608, "dur": 35, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095644, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095646, "dur": 34, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095682, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095684, "dur": 34, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095722, "dur": 34, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095758, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095760, "dur": 30, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095791, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095793, "dur": 31, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095827, "dur": 44, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095874, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095877, "dur": 43, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095923, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095925, "dur": 36, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095964, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600095965, "dur": 37, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096006, "dur": 29, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096038, "dur": 35, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096076, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096078, "dur": 50, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096131, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096133, "dur": 38, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096174, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096176, "dur": 38, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096217, "dur": 31, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096251, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096252, "dur": 31, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096287, "dur": 38, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096328, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096330, "dur": 43, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096376, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096378, "dur": 41, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096429, "dur": 44, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096476, "dur": 1, "ph": "X", "name": "ProcessMessages 1033", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096478, "dur": 29, "ph": "X", "name": "ReadAsync 1033", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096510, "dur": 36, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096549, "dur": 35, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096587, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096588, "dur": 38, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096628, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096630, "dur": 43, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096676, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096677, "dur": 33, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096714, "dur": 38, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096755, "dur": 35, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096792, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096794, "dur": 33, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096829, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096830, "dur": 35, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096868, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096870, "dur": 45, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096918, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096920, "dur": 39, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600096963, "dur": 38, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097004, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097005, "dur": 36, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097044, "dur": 36, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097084, "dur": 31, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097117, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097118, "dur": 42, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097164, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097166, "dur": 45, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097213, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097214, "dur": 23, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097241, "dur": 37, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097282, "dur": 71, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097357, "dur": 2, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097360, "dur": 115, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097479, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097482, "dur": 54, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097539, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097541, "dur": 50, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097594, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097597, "dur": 67, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097667, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097670, "dur": 40, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097712, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097714, "dur": 34, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097750, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097752, "dur": 34, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097789, "dur": 31, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097822, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097824, "dur": 38, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600097865, "dur": 147, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098015, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098017, "dur": 53, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098073, "dur": 2, "ph": "X", "name": "ProcessMessages 1801", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098076, "dur": 41, "ph": "X", "name": "ReadAsync 1801", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098121, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098160, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098162, "dur": 37, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098203, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098206, "dur": 39, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098247, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098249, "dur": 39, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098291, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098293, "dur": 31, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098327, "dur": 40, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098370, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098372, "dur": 36, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098410, "dur": 2, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098413, "dur": 32, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098448, "dur": 1, "ph": "X", "name": "ProcessMessages 191", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098451, "dur": 46, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098500, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098501, "dur": 47, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098552, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098555, "dur": 40, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098597, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098599, "dur": 37, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098639, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098641, "dur": 32, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098675, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098677, "dur": 37, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098717, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098719, "dur": 51, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098773, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600098775, "dur": 4320, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103100, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103149, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103152, "dur": 42, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103197, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103199, "dur": 46, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103248, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103286, "dur": 41, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103331, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103333, "dur": 38, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103374, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103376, "dur": 42, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103421, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103423, "dur": 37, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103463, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103466, "dur": 45, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103514, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103515, "dur": 79, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103598, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103601, "dur": 47, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103651, "dur": 1, "ph": "X", "name": "ProcessMessages 1127", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103653, "dur": 51, "ph": "X", "name": "ReadAsync 1127", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103706, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103708, "dur": 38, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103749, "dur": 41, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103792, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103794, "dur": 38, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103834, "dur": 35, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103873, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103876, "dur": 41, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103922, "dur": 46, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103971, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600103974, "dur": 89, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104066, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104068, "dur": 41, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104114, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104159, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104161, "dur": 36, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104200, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104202, "dur": 39, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104245, "dur": 72, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104321, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104360, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104362, "dur": 39, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104404, "dur": 36, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104442, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104443, "dur": 85, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104532, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104534, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104576, "dur": 2, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104579, "dur": 40, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104622, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104623, "dur": 30, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104656, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104657, "dur": 73, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104734, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104736, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104783, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104786, "dur": 38, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104827, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104828, "dur": 89, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104922, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104968, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600104970, "dur": 38, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105011, "dur": 28, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105043, "dur": 84, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105130, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105132, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105178, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105180, "dur": 33, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105217, "dur": 32, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105251, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105254, "dur": 78, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105336, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105378, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105379, "dur": 37, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105420, "dur": 30, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105453, "dur": 77, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105532, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105535, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105577, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105578, "dur": 35, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105616, "dur": 30, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105650, "dur": 77, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105730, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105770, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105771, "dur": 35, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105808, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105810, "dur": 31, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105844, "dur": 81, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105929, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600105968, "dur": 29, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106002, "dur": 34, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106037, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106039, "dur": 84, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106127, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106167, "dur": 35, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106204, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106206, "dur": 30, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106238, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106239, "dur": 78, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106321, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106360, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106362, "dur": 34, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106400, "dur": 31, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106433, "dur": 2, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106436, "dur": 80, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106519, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106560, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106562, "dur": 32, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106596, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106598, "dur": 30, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106632, "dur": 76, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106712, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106750, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106751, "dur": 34, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106787, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106789, "dur": 33, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106824, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106826, "dur": 86, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106916, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106953, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106955, "dur": 35, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106993, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600106995, "dur": 80, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107078, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107117, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107119, "dur": 37, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107159, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107160, "dur": 28, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107192, "dur": 67, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107263, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107302, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107303, "dur": 34, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107340, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107341, "dur": 31, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107376, "dur": 80, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107459, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107497, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107499, "dur": 31, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107532, "dur": 1, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107534, "dur": 35, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107573, "dur": 35, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107609, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107611, "dur": 28, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107642, "dur": 65, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107711, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107750, "dur": 33, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107785, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107786, "dur": 35, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107824, "dur": 1, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107826, "dur": 34, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107862, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107864, "dur": 34, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107902, "dur": 33, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107938, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107939, "dur": 35, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600107978, "dur": 27, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108009, "dur": 75, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108088, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108127, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108130, "dur": 38, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108169, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108171, "dur": 29, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108204, "dur": 70, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108278, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108316, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108318, "dur": 33, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108353, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108355, "dur": 30, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108389, "dur": 72, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108464, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108501, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108503, "dur": 32, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108537, "dur": 2, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108540, "dur": 37, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108580, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108582, "dur": 35, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108619, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108622, "dur": 35, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108660, "dur": 31, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108694, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108696, "dur": 30, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108729, "dur": 29, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108761, "dur": 69, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108834, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108873, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108874, "dur": 36, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108912, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108914, "dur": 31, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600108949, "dur": 86, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109038, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109080, "dur": 33, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109116, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109117, "dur": 30, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109151, "dur": 79, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109234, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109271, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109272, "dur": 33, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109309, "dur": 34, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109345, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109347, "dur": 34, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109383, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109385, "dur": 31, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109419, "dur": 35, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109458, "dur": 31, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109492, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109494, "dur": 32, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109527, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109529, "dur": 89, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109621, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109659, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109661, "dur": 34, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109698, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109699, "dur": 31, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109732, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109734, "dur": 80, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109817, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109858, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109860, "dur": 33, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109895, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109897, "dur": 31, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109930, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600109932, "dur": 76, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110012, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110014, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110053, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110055, "dur": 36, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110093, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110095, "dur": 28, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110127, "dur": 82, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110212, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110252, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110253, "dur": 34, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110290, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110292, "dur": 34, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110330, "dur": 35, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110367, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110369, "dur": 34, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110406, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110409, "dur": 76, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110489, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110531, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110533, "dur": 38, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110573, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110575, "dur": 28, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110606, "dur": 73, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110682, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110723, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110725, "dur": 33, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110760, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110761, "dur": 30, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110795, "dur": 74, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110872, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110916, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110918, "dur": 35, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110957, "dur": 33, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110993, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600110994, "dur": 81, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111079, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111118, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111119, "dur": 32, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111154, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111156, "dur": 30, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111189, "dur": 109, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111303, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111349, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111351, "dur": 39, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111393, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111395, "dur": 83, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111483, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111524, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111526, "dur": 38, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111567, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111569, "dur": 29, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111601, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111603, "dur": 68, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111675, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111716, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111719, "dur": 38, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111760, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111762, "dur": 32, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111796, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111798, "dur": 71, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111874, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111913, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111916, "dur": 37, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111955, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600111957, "dur": 84, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112045, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112086, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112088, "dur": 38, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112129, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112131, "dur": 33, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112168, "dur": 67, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112239, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112279, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112281, "dur": 38, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112321, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112323, "dur": 77, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112403, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112441, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112443, "dur": 38, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112483, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112485, "dur": 36, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112524, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112526, "dur": 77, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112607, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112649, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112651, "dur": 36, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112689, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112691, "dur": 84, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112779, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112821, "dur": 2, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112824, "dur": 40, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112866, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112868, "dur": 30, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112900, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112902, "dur": 66, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600112972, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113012, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113014, "dur": 37, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113053, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113055, "dur": 87, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113146, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113186, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113188, "dur": 37, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113228, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113230, "dur": 75, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113310, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113349, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113351, "dur": 39, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113393, "dur": 1, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113395, "dur": 35, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113434, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113436, "dur": 88, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113529, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113576, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113578, "dur": 36, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113616, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113618, "dur": 85, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113707, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113748, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113750, "dur": 35, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113789, "dur": 28, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113822, "dur": 85, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113910, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113946, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113948, "dur": 35, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113985, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600113988, "dur": 32, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114022, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114024, "dur": 78, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114105, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114144, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114146, "dur": 34, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114182, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114184, "dur": 31, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114219, "dur": 85, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114307, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114343, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114345, "dur": 33, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114381, "dur": 47, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114431, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114433, "dur": 41, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114477, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114480, "dur": 34, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114516, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114517, "dur": 35, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114555, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114556, "dur": 27, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114587, "dur": 31, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114623, "dur": 75, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114701, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114744, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114746, "dur": 37, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114785, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114787, "dur": 38, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114828, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114830, "dur": 35, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114867, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114869, "dur": 35, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114906, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114908, "dur": 32, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114942, "dur": 1, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114944, "dur": 32, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600114979, "dur": 82, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600115065, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600115111, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600115113, "dur": 32, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600115149, "dur": 39, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600115190, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600115192, "dur": 42, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600115236, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600115237, "dur": 34, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600115274, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600115276, "dur": 31, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600115310, "dur": 30, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600115345, "dur": 90, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600115438, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600115482, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600115484, "dur": 76, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600115564, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600115567, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600115617, "dur": 385, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116005, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116084, "dur": 10, "ph": "X", "name": "ProcessMessages 1084", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116096, "dur": 37, "ph": "X", "name": "ReadAsync 1084", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116136, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116139, "dur": 38, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116179, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116181, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116214, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116217, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116254, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116257, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116294, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116297, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116335, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116338, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116378, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116381, "dur": 34, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116418, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116421, "dur": 37, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116462, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116464, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116504, "dur": 2, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116507, "dur": 35, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116546, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116548, "dur": 33, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116585, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116588, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116621, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116624, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116660, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116664, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116700, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116703, "dur": 38, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116744, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116746, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116784, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116787, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116826, "dur": 2, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116829, "dur": 35, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116867, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116870, "dur": 35, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116908, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116911, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116949, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116951, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116986, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600116988, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117022, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117024, "dur": 33, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117061, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117065, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117101, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117104, "dur": 36, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117144, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117147, "dur": 33, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117184, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117186, "dur": 36, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117225, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117228, "dur": 35, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117266, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117268, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117305, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117308, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117338, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117340, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117374, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117377, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117411, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117413, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117447, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117450, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117486, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117491, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117526, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117530, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117564, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117567, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117608, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117611, "dur": 28, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117641, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117644, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117683, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117686, "dur": 33, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117722, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117725, "dur": 35, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117763, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117765, "dur": 32, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117800, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117802, "dur": 30, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117836, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117838, "dur": 33, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117874, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117876, "dur": 32, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117911, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117913, "dur": 34, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117950, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117953, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117985, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600117987, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118024, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118027, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118073, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118076, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118118, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118121, "dur": 36, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118160, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118163, "dur": 34, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118202, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118205, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118242, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118246, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118283, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118285, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118317, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118320, "dur": 32, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118355, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118357, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118388, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118390, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118426, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118429, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118476, "dur": 109, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118590, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118592, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118627, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118632, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118669, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118673, "dur": 28, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118705, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118708, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118743, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118745, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118782, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118785, "dur": 36, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118824, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118827, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118862, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118865, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118901, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118903, "dur": 30, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118935, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118937, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118969, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600118971, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119004, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119006, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119042, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119044, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119076, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119078, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119120, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119123, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119162, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119165, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119206, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119209, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119245, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119247, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119281, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119284, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119320, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119323, "dur": 29, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119355, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119357, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119391, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119394, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119427, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119429, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119460, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119462, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119504, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119506, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119531, "dur": 108, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119642, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119644, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119675, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600119677, "dur": 570, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600120250, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600120252, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600120285, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600120287, "dur": 651, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600120942, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600120944, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600120979, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600120981, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600121016, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600121020, "dur": 129, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600121152, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600121154, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600121185, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600121188, "dur": 5647, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600126842, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600126846, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600126894, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600126896, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600126935, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600126937, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600126972, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600126974, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600127058, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600127061, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600127102, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600127104, "dur": 1032, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600128139, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600128142, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600128177, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600128179, "dur": 197, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600128379, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600128381, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600128415, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600128446, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600128493, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600128536, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600128538, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600128572, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600128574, "dur": 208, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600128789, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600128821, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600128823, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600128931, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600128963, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600128966, "dur": 204, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600129173, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600129175, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600129213, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600129216, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600129251, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600129254, "dur": 184, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600129443, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600129483, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600129485, "dur": 113, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600129603, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600129638, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600129672, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600129674, "dur": 116, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600129794, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600129825, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600129827, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600129878, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600129880, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600129917, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600129919, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600129997, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600129999, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130050, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130053, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130087, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130089, "dur": 124, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130216, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130218, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130264, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130266, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130309, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130311, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130350, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130353, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130389, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130393, "dur": 113, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130511, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130548, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130550, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130590, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130593, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130625, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130627, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130666, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130699, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130701, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130736, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130738, "dur": 33, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130774, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130776, "dur": 158, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130940, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130973, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600130975, "dur": 184, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600131163, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600131165, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600131207, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600131211, "dur": 72, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600131286, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600131289, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600131322, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600131325, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600131386, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600131422, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600131426, "dur": 29, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600131459, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600131505, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600131508, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600131543, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600131545, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600131581, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600131585, "dur": 468, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132056, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132059, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132094, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132096, "dur": 103, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132203, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132205, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132242, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132245, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132312, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132314, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132350, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132352, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132384, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132386, "dur": 186, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132581, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132617, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132619, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132655, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132657, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132692, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132694, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132727, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132729, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132796, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132829, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132831, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132864, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600132897, "dur": 170, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600133072, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600133106, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600133108, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600133178, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600133225, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600133227, "dur": 383, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600133613, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600133615, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600133653, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600133656, "dur": 150, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600133809, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600133811, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600133846, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600133848, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600133882, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600133914, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600133916, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600133948, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600133949, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600133982, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600133984, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600134019, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600134022, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600134070, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600134072, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600134104, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600134106, "dur": 28, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600134137, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600134139, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600134171, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600134174, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600134210, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600134212, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600134263, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600134265, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600134291, "dur": 220, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600134517, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600134553, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600134555, "dur": 161, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600134737, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600134773, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600134775, "dur": 188, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600134966, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600134968, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600135004, "dur": 716, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600135724, "dur": 43, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600135771, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600135775, "dur": 368, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600136147, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600136149, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600136197, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600136200, "dur": 139, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600136342, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600136345, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600136384, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600136387, "dur": 180, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600136571, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600136573, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600136607, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600136610, "dur": 270, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600136884, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600136886, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600136928, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600136931, "dur": 159, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600137096, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600137135, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600137137, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600137176, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600137179, "dur": 299, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600137482, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600137484, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600137522, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600137524, "dur": 185, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600137712, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600137716, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600137770, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600137773, "dur": 42, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600137819, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600137821, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600137858, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600137861, "dur": 415, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600138279, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600138281, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600138316, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600138319, "dur": 29, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600138350, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600138352, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600138391, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600138426, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600138427, "dur": 111, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600138543, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600138545, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600138587, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600138589, "dur": 77, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600138670, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600138672, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600138741, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600138744, "dur": 801, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600139549, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600139552, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600139593, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600139596, "dur": 173, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600139773, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600139813, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600139815, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600139855, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600139857, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600139898, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600139901, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600139998, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600140034, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600140036, "dur": 151, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600140191, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600140194, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600140232, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600140235, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600140276, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600140279, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600140347, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600140350, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600140390, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600140392, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600140430, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600140432, "dur": 132, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600140568, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600140570, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600140628, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600140630, "dur": 124, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600140758, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600140761, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600140810, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600140812, "dur": 568, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600141383, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600141385, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600141430, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600141432, "dur": 229, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600141665, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600141669, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600141721, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600141724, "dur": 544, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600142273, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600142275, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600142321, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600142323, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600142366, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600142368, "dur": 120, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600142493, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600142495, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600142540, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600142542, "dur": 210, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600142771, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600142773, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600142821, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600142824, "dur": 143, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600142980, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600142983, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600143029, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600143031, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600143074, "dur": 391, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600143469, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600143471, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600143516, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600143518, "dur": 927, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600144450, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600144454, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600144556, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600144560, "dur": 65226, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600209796, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600209800, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600209865, "dur": 2356, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600212229, "dur": 3023, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600215259, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600215263, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600215337, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600215340, "dur": 172, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600215517, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600215519, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600215567, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600215571, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600215609, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600215612, "dur": 1488, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600217105, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600217108, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600217148, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600217150, "dur": 739, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600217894, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600217896, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600217937, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600217940, "dur": 118, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600218061, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600218064, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600218100, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600218103, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600218154, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600218157, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600218191, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600218193, "dur": 752, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600218950, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600218953, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600218989, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600218992, "dur": 532, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600219529, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600219563, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600219566, "dur": 861, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600220431, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600220433, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600220460, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600220462, "dur": 256, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600220722, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600220725, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600220776, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600220778, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600220815, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600220818, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600220879, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600220913, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600220915, "dur": 554, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600221474, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600221508, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600221510, "dur": 366, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600221880, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600221917, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600221919, "dur": 968, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600222893, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600222905, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600222941, "dur": 35, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600222989, "dur": 377, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600223371, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600223373, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600223413, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600223416, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600223510, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600223512, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600223550, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600223552, "dur": 127, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600223683, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600223686, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600223714, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600223716, "dur": 399, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600224120, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600224157, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600224159, "dur": 245, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600224408, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600224410, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600224447, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600224449, "dur": 919, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600225372, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600225374, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600225410, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600225412, "dur": 481, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600225897, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600225932, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600225934, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600225971, "dur": 204, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600226180, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600226212, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600226214, "dur": 318, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600226535, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600226537, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600226571, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600226574, "dur": 401, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600226980, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600227014, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600227017, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600227070, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600227072, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600227108, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600227110, "dur": 506, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600227620, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600227622, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600227664, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600227667, "dur": 684, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600228354, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600228357, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600228398, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600228401, "dur": 557, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600228962, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600228964, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600229009, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600229012, "dur": 524, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600229541, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600229543, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600229588, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600229590, "dur": 139, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600229733, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600229736, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600229775, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600229778, "dur": 978, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600230762, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600230764, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600230788, "dur": 420, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600231212, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600231215, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600231258, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600231260, "dur": 696, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600231961, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600231967, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600232013, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600232016, "dur": 1146, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600233168, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600233171, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600233219, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600233222, "dur": 359, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600233586, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600233588, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600233641, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600233644, "dur": 734, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600234382, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600234386, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600234436, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600234439, "dur": 1179, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600235624, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600235626, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600235674, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600235677, "dur": 41, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600235722, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600235725, "dur": 35, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600235763, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600235766, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600235808, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600235810, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600235860, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600235863, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600235901, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600235904, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600235947, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600235952, "dur": 37, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600235993, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600235996, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236039, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236042, "dur": 43, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236090, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236093, "dur": 38, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236135, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236137, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236181, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236184, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236225, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236227, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236271, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236274, "dur": 40, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236318, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236321, "dur": 35, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236360, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236363, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236405, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236408, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236452, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236455, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236492, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236495, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236542, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236545, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236586, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236589, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236635, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236638, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236682, "dur": 10, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236693, "dur": 70, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236768, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236771, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236812, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236815, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236856, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236859, "dur": 43, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236905, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236907, "dur": 37, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236949, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236952, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236995, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600236998, "dur": 38, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600237040, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600237043, "dur": 40, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600237087, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600237091, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600237135, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600237137, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600237195, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600237197, "dur": 112107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600349315, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600349319, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600349385, "dur": 25, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600349412, "dur": 2231, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600351651, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600351656, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600351721, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600351726, "dur": 32030, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600383766, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600383771, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600383819, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600383824, "dur": 55, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600383884, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600383919, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600383922, "dur": 102022, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600485954, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600485958, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600486015, "dur": 23, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600486040, "dur": 1533, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600487579, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600487581, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600487630, "dur": 25, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600487657, "dur": 8467, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600496136, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600496141, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600496206, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600496210, "dur": 2379, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600498600, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600498607, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600498657, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600498662, "dur": 2405, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600501079, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600501086, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600501155, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600501186, "dur": 18915, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600520111, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600520116, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600520149, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600520151, "dur": 7058, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600527220, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600527224, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600527275, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600527280, "dur": 1165, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600528456, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600528462, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600528519, "dur": 25, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600528546, "dur": 465, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600529016, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600529019, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600529066, "dur": 405, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 17180, "tid": 12884901888, "ts": 1752722600529476, "dur": 10868, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 17180, "tid": 1450, "ts": 1752722600561768, "dur": 3513, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 17180, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 17180, "tid": 8589934592, "ts": 1752722600070296, "dur": 117921, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 17180, "tid": 8589934592, "ts": 1752722600188221, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 17180, "tid": 8589934592, "ts": 1752722600188229, "dur": 1476, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 17180, "tid": 1450, "ts": 1752722600565284, "dur": 9, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 17180, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 17180, "tid": 4294967296, "ts": 1752722600049942, "dur": 491839, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 17180, "tid": 4294967296, "ts": 1752722600053805, "dur": 8349, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 17180, "tid": 4294967296, "ts": 1752722600542050, "dur": 6673, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 17180, "tid": 4294967296, "ts": 1752722600546053, "dur": 148, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 17180, "tid": 4294967296, "ts": 1752722600548857, "dur": 16, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 17180, "tid": 1450, "ts": 1752722600565295, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752722600078869, "dur": 2378, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752722600081262, "dur": 1034, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752722600082457, "dur": 87, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1752722600082544, "dur": 385, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752722600084359, "dur": 473, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3BACF753C5B41AC5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752722600086101, "dur": 1332, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752722600088176, "dur": 130, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752722600095579, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752722600097672, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_159E061D77A10B86.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752722600098327, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1752722600103458, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1752722600082965, "dur": 32764, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752722600115743, "dur": 413141, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752722600528887, "dur": 71, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752722600528959, "dur": 81, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752722600529173, "dur": 79, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752722600529289, "dur": 2123, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752722600083208, "dur": 32546, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752722600115834, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1752722600115950, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_9D0087D0CAC25D2C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752722600116339, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_1AA34DA22D3674A5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752722600116451, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_DB68F65C9F3573CD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752722600116609, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752722600116778, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752722600116851, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752722600117031, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752722600117138, "dur": 4040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752722600121250, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752722600121431, "dur": 5619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752722600127151, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752722600127337, "dur": 1037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752722600128458, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752722600128658, "dur": 1827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752722600130485, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752722600130538, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752722600130650, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752722600130869, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752722600131445, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752722600131659, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752722600131717, "dur": 719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752722600132481, "dur": 803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752722600133285, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752722600133350, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752722600133884, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752722600134336, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752722600135047, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752722600135226, "dur": 327, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752722600135555, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752722600136048, "dur": 610, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752722600136659, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752722600136853, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752722600137369, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752722600137800, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752722600137966, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752722600138115, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752722600138600, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752722600138744, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752722600138967, "dur": 800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752722600139840, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752722600140049, "dur": 731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752722600140862, "dur": 52269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752722600193166, "dur": 22626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752722600215793, "dur": 2328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752722600218179, "dur": 2440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752722600220620, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752722600220729, "dur": 3020, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752722600223778, "dur": 179, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752722600223961, "dur": 2441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752722600226460, "dur": 10835, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752722600237350, "dur": 291433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600083262, "dur": 32507, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600115782, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600115872, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1752722600116337, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_622A1FF3909771E4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752722600116436, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_13694A2D45790405.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752722600116841, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600116917, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752722600117122, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752722600117221, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1752722600117564, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752722600117877, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600117948, "dur": 278, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1752722600118487, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752722600118708, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600119055, "dur": 336, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1752722600119708, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600119940, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600120147, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600120519, "dur": 576, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\Editior\\6000.0.34f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-crt-time-l1-1-0.dll"}}, {"pid": 12345, "tid": 2, "ts": 1752722600120343, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600121238, "dur": 1804, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Editor\\TMP\\TMP_BaseShaderGUI.cs"}}, {"pid": 12345, "tid": 2, "ts": 1752722600121107, "dur": 2000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600123107, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600123311, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600123511, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600123713, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600123909, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600124131, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600124388, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600124595, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600124849, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600125073, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600125288, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600125516, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600125730, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600125979, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600126205, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600126426, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600126671, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600127285, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600127557, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600127827, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600128223, "dur": 1050, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections\\Unity.Collections\\ListExtensions.cs"}}, {"pid": 12345, "tid": 2, "ts": 1752722600129274, "dur": 1118, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections\\Unity.Collections\\Jobs\\RegisterGenericJobTypeAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": 1752722600130392, "dur": 1086, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections\\Unity.Collections\\Jobs\\IJobParallelForDefer.cs"}}, {"pid": 12345, "tid": 2, "ts": 1752722600131478, "dur": 907, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections\\Unity.Collections\\Jobs\\IJobParallelForBatch.cs"}}, {"pid": 12345, "tid": 2, "ts": 1752722600128074, "dur": 4332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600132407, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752722600132963, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752722600133166, "dur": 858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752722600134026, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600134248, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_3A975DBA53ABA4AD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752722600134435, "dur": 355, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.CodeGen.dll"}}, {"pid": 12345, "tid": 2, "ts": 1752722600134987, "dur": 1357, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Plugins\\Android\\AndroidSupport.cs"}}, {"pid": 12345, "tid": 2, "ts": 1752722600134792, "dur": 1557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600136349, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600136655, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752722600136865, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752722600137445, "dur": 631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600138076, "dur": 592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600138668, "dur": 1210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600139880, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752722600140131, "dur": 1476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752722600141668, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752722600141934, "dur": 1008, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752722600143019, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752722600143206, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752722600143689, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752722600144591, "dur": 96, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600145700, "dur": 203868, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752722600351602, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 2, "ts": 1752722600351238, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752722600351894, "dur": 27480, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1752722600351891, "dur": 28807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752722600382082, "dur": 193, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752722600384792, "dur": 101416, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752722600495932, "dur": 384, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1752722600495918, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1752722600496385, "dur": 32389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600083301, "dur": 32482, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600115790, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B9448EEF9FF8FB2A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752722600116314, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_5DB2248094ADF2F7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752722600116422, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600116664, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_AAC2C267FBE7A1FE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752722600117024, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752722600117163, "dur": 3319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752722600120558, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600120755, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600120953, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600121180, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600121408, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600121627, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600121852, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600122057, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600122266, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600122477, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600123095, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600123286, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600123485, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600123711, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600123909, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600124133, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600124362, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600124559, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600124807, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600125018, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600125244, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600125468, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600125700, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600125952, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600126177, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600126415, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600126670, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600126880, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600127378, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600127942, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600128165, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600128369, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600128766, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752722600129065, "dur": 774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752722600129932, "dur": 944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752722600130878, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600130982, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752722600131219, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752722600131449, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752722600131664, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752722600131825, "dur": 1055, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752722600132929, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752722600133113, "dur": 1306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752722600134482, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600135055, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600135314, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600136048, "dur": 627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600136675, "dur": 1127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600137803, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752722600138003, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752722600138545, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600138686, "dur": 2991, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600141679, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752722600141904, "dur": 557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752722600142531, "dur": 70945, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600213480, "dur": 2274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752722600215755, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600215860, "dur": 2447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752722600218309, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600218435, "dur": 2587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752722600221058, "dur": 2588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752722600223647, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600223795, "dur": 2333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752722600226170, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752722600226243, "dur": 2342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752722600228630, "dur": 2336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752722600231028, "dur": 2356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752722600233433, "dur": 2412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752722600236045, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600236466, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1752722600236604, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600236907, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1752722600237132, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1752722600237282, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752722600237374, "dur": 291369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600083346, "dur": 32453, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600115805, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1D503C7A8290C7BE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752722600116318, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_B7F547A15B2C01B2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752722600116468, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_6A35B4E7D99F65C8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752722600117193, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752722600117370, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600117510, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752722600117932, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600118323, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1752722600118713, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600119056, "dur": 286, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1752722600119578, "dur": 361, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752722600119942, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600120392, "dur": 379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600120771, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600120972, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600121217, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600121563, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600121780, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600121999, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600122199, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600122425, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600123023, "dur": 693, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Nodes\\Math\\Advanced\\ReciprocalSquareRootNode.cs"}}, {"pid": 12345, "tid": 4, "ts": 1752722600122621, "dur": 1255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600123876, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600124095, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600124322, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600124526, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600124788, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600125003, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600125234, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600125452, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600125742, "dur": 1041, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Utilities\\VSBackupUtility.cs"}}, {"pid": 12345, "tid": 4, "ts": 1752722600125699, "dur": 1249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600126948, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600127209, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600127498, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600127742, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600127968, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600128187, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600128411, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600128622, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752722600128821, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752722600129475, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752722600129723, "dur": 506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752722600130296, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752722600130488, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752722600130545, "dur": 5813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752722600136430, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752722600136615, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752722600137157, "dur": 643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600137802, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752722600137994, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752722600138598, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752722600138852, "dur": 1540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752722600140393, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600140487, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752722600140674, "dur": 917, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752722600141675, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752722600141918, "dur": 641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752722600142606, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752722600142721, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752722600143031, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752722600143271, "dur": 72585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600215858, "dur": 2431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752722600218339, "dur": 2623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752722600220992, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752722600221053, "dur": 3588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752722600224693, "dur": 2548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752722600227242, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600227346, "dur": 8484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752722600235831, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600235990, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600236079, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 4, "ts": 1752722600236430, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1752722600236517, "dur": 202, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1752722600236721, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.ConversionSystem.dll"}}, {"pid": 12345, "tid": 4, "ts": 1752722600236787, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600237126, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1752722600237318, "dur": 261075, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752722600498418, "dur": 382, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1752722600498394, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1752722600498850, "dur": 2465, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1752722600501324, "dur": 27423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600083389, "dur": 32421, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600115815, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_40A52831373DC9B0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752722600115874, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600115992, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600116326, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_611E434C56DD2756.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752722600116409, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_40E8F641E9958680.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752722600117111, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752722600117181, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1752722600117243, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600117763, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1752722600118212, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752722600118389, "dur": 224, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1752722600118739, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600118873, "dur": 208, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752722600119309, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752722600119666, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600119923, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600120206, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600120439, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600120626, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600120819, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600121038, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600121286, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600121502, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600121722, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600121962, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600122177, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600122385, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600122587, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600123185, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600123383, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600123576, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600123788, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600124012, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600124225, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600124444, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600124660, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600124908, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600125130, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600125375, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600125606, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600125846, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600126065, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600126274, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600126514, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600126756, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600126976, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600127195, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600127470, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600127745, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600127970, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600128180, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600128384, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600128597, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752722600128825, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752722600129490, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752722600129721, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752722600129919, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752722600130627, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752722600130787, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752722600131032, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752722600131692, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752722600132334, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752722600132842, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600133075, "dur": 1045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752722600134120, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600134283, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600134543, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600134954, "dur": 85, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600135039, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600135285, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600135546, "dur": 535, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\UI\\TabButton.cs"}}, {"pid": 12345, "tid": 5, "ts": 1752722600135531, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600136086, "dur": 566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600136653, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752722600136864, "dur": 1443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752722600138308, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600138587, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752722600138817, "dur": 1405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752722600140298, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752722600140529, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752722600141044, "dur": 72437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600213483, "dur": 1962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752722600215494, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752722600215604, "dur": 5210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752722600220815, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600221158, "dur": 2439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752722600223644, "dur": 2424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752722600226069, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600226176, "dur": 3008, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752722600229222, "dur": 7121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752722600236377, "dur": 234, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1752722600236627, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600236714, "dur": 205, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1752722600237009, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1752722600237299, "dur": 113948, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600351273, "dur": 28102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1752722600351250, "dur": 29444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752722600383956, "dur": 189, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752722600384802, "dur": 103142, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752722600498370, "dur": 29052, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1752722600498355, "dur": 29074, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1752722600527456, "dur": 1246, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1752722600083433, "dur": 32387, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600115826, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_5BB3CA76865503EA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752722600115990, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_D397B83A68A481D1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752722600116340, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_D397B83A68A481D1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752722600116460, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_5F12F6E926C28756.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752722600116580, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_BD82F057A3253721.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752722600116650, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_BD82F057A3253721.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752722600116833, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600117233, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752722600117428, "dur": 344, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752722600117884, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1752722600118068, "dur": 280, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1752722600118408, "dur": 309, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1752722600118746, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600119591, "dur": 222, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752722600119814, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600120062, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600120265, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600120561, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600120784, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600120984, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600121338, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600121579, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600121804, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600122016, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600122222, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600122440, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600122645, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600123245, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600123455, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600123686, "dur": 614, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.State\\Flow\\StateUnitEditor.cs"}}, {"pid": 12345, "tid": 6, "ts": 1752722600123665, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600124478, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600124710, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600124935, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600125168, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600125398, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600125684, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600125928, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600126162, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600126372, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600126597, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600126828, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600127055, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600127260, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600127501, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600127802, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600128038, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600128258, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600128552, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752722600128766, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752722600128822, "dur": 7738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752722600136651, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752722600136870, "dur": 843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752722600137797, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752722600137988, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752722600138697, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752722600138945, "dur": 815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752722600139761, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600139845, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752722600140052, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752722600140645, "dur": 49329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600191220, "dur": 275, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 6, "ts": 1752722600191495, "dur": 1448, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 6, "ts": 1752722600192944, "dur": 178, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 6, "ts": 1752722600189976, "dur": 3152, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600193129, "dur": 21707, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600214838, "dur": 2385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752722600217229, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752722600217353, "dur": 2396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752722600219808, "dur": 2300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752722600222160, "dur": 2482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752722600224699, "dur": 2516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752722600227258, "dur": 2501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752722600229809, "dur": 2369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752722600232233, "dur": 2419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752722600234698, "dur": 2277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752722600237326, "dur": 291441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600083483, "dur": 32344, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600115872, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600116335, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_DE036AB7E94B4D31.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752722600116419, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_0C31EB717FD7AB62.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752722600117031, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752722600117171, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1752722600117457, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1752722600117546, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752722600118027, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752722600118311, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752722600118596, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752722600118738, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600119659, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600119878, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600120206, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600120419, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600120614, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600120810, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600121032, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600121273, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600121526, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600121755, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600121995, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600122225, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600122451, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600123034, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600123244, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600123436, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600123644, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600123850, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600124076, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600124298, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600124508, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600124770, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600124982, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600125204, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600125419, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600125644, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600125900, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600126110, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600126323, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600126537, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600126762, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600126968, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600127211, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600127455, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600127673, "dur": 559, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Graphs\\IGraphData.cs"}}, {"pid": 12345, "tid": 7, "ts": 1752722600127673, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600128434, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600128705, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752722600128917, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600129210, "dur": 823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752722600130081, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600130155, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752722600130358, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752722600131015, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752722600131235, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752722600131446, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752722600131667, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752722600131842, "dur": 509, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600132357, "dur": 586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752722600132990, "dur": 1241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752722600134232, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600134463, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600134959, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600135185, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600135524, "dur": 534, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\Changesets\\ChangesetsListHeaderState.cs"}}, {"pid": 12345, "tid": 7, "ts": 1752722600135404, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600136090, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600136670, "dur": 1193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600137864, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752722600138096, "dur": 806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752722600138974, "dur": 4065, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600143040, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752722600143297, "dur": 70182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600213481, "dur": 2246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752722600215789, "dur": 3386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752722600219226, "dur": 2464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752722600221691, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600221753, "dur": 2525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752722600224280, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600224397, "dur": 2361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752722600226817, "dur": 2373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752722600229236, "dur": 2192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752722600231485, "dur": 2313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752722600233858, "dur": 2320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752722600236435, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1752722600236703, "dur": 226, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.ConversionSystem.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752722600236959, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600237185, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752722600237309, "dur": 258622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752722600495957, "dur": 24337, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1752722600495932, "dur": 24363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1752722600520340, "dur": 8426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600083529, "dur": 32311, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600115841, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_1D34F993682102CF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752722600116318, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_DF7681D3753AB490.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752722600116423, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_A7F411FD3CF1CCBD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752722600116775, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1752722600117120, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752722600117297, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_0B080C2548A382FC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752722600117600, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752722600117676, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752722600117996, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752722600118745, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600118946, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752722600119678, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600119889, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600120126, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600120317, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600120529, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600120718, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600120915, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600121285, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600121486, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600121693, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600121916, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600122121, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600122343, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600122548, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600123141, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600123333, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600123539, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600123756, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600123988, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600124195, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600124429, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600124669, "dur": 1070, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Flow\\Description\\UnitPortDescriptor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1752722600124636, "dur": 1334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600125971, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600126185, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600126404, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600126652, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600126865, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600127294, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600127588, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600127853, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600128090, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600128306, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600128518, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752722600128703, "dur": 2824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752722600131592, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752722600131785, "dur": 731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752722600132517, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600132646, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752722600132875, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752722600133369, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600133448, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752722600133509, "dur": 936, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1752722600134507, "dur": 249, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600135248, "dur": 74805, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1752722600213476, "dur": 2275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752722600215809, "dur": 2312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752722600218168, "dur": 2450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752722600220619, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600220704, "dur": 2388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752722600223141, "dur": 2454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752722600225649, "dur": 2173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752722600227823, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600227899, "dur": 2043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752722600229996, "dur": 2222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752722600232265, "dur": 2292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752722600234559, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600234654, "dur": 2215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752722600237132, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1752722600237197, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752722600237429, "dur": 291348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752722600538223, "dur": 1946, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 17180, "tid": 1450, "ts": 1752722600565874, "dur": 3401, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 17180, "tid": 1450, "ts": 1752722600569326, "dur": 3348, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 17180, "tid": 1450, "ts": 1752722600557642, "dur": 16332, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}