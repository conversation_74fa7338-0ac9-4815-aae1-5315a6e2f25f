using UnityEngine;

[System.Serializable]
public class TerrainShaderTester : MonoBehaviour
{
    [Header("Test Material")]
    public Material terrainMaterial;
    
    [Header("Test Textures")]
    public Texture2D controlTexture;
    public Texture2D[] splatTextures = new Texture2D[4];
    public Texture2D terrainMask;
    
    [Header("Color Settings")]
    public Color darkColor = new Color(0.2f, 0.3f, 0.1f, 1f);
    public Color midColor = new Color(0.4f, 0.6f, 0.2f, 1f);
    public Color lightColor = new Color(0.7f, 0.9f, 0.3f, 1f);
    
    [Header("Shader Parameters")]
    [Range(-1f, 1f)]
    public float gradientOffset = 0f;
    [Range(0f, 1f)]
    public float colorBlend = 0.8f;
    [Range(0.5f, 2f)]
    public float brightness = 1f;
    [Range(0f, 1f)]
    public float metallic = 0f;
    [Range(0f, 1f)]
    public float smoothness = 0.5f;
    
    void Start()
    {
        if (terrainMaterial == null)
        {
            // Try to find a material using the terrain shader
            Renderer renderer = GetComponent<Renderer>();
            if (renderer != null)
            {
                terrainMaterial = renderer.material;
            }
        }
        
        UpdateShaderProperties();
    }
    
    void OnValidate()
    {
        if (Application.isPlaying)
        {
            UpdateShaderProperties();
        }
    }
    
    public void UpdateShaderProperties()
    {
        if (terrainMaterial == null) return;
        
        // Set textures
        if (controlTexture != null)
            terrainMaterial.SetTexture("_Control", controlTexture);
            
        for (int i = 0; i < splatTextures.Length; i++)
        {
            if (splatTextures[i] != null)
                terrainMaterial.SetTexture($"_Splat{i}", splatTextures[i]);
        }
        
        if (terrainMask != null)
            terrainMaterial.SetTexture("_TerrainMask", terrainMask);
        
        // Set colors
        terrainMaterial.SetColor("_DarkColor", darkColor);
        terrainMaterial.SetColor("_MidColor", midColor);
        terrainMaterial.SetColor("_LightColor", lightColor);
        
        // Set parameters
        terrainMaterial.SetFloat("_ColorGradientOffset", gradientOffset);
        terrainMaterial.SetFloat("_ColorBlend", colorBlend);
        terrainMaterial.SetFloat("_Brightness", brightness);
        terrainMaterial.SetFloat("_Metallic", metallic);
        terrainMaterial.SetFloat("_Smoothness", smoothness);
    }
    
    [ContextMenu("Create Test Textures")]
    public void CreateTestTextures()
    {
        // Create a simple control texture for testing
        if (controlTexture == null)
        {
            controlTexture = CreateTestControlTexture();
        }
        
        // Create simple test splat textures if missing
        for (int i = 0; i < splatTextures.Length; i++)
        {
            if (splatTextures[i] == null)
            {
                splatTextures[i] = CreateTestSplatTexture(i);
            }
        }
        
        // Create a test terrain mask
        if (terrainMask == null)
        {
            terrainMask = CreateTestTerrainMask();
        }
        
        UpdateShaderProperties();
    }
    
    private Texture2D CreateTestControlTexture()
    {
        int size = 256;
        Texture2D tex = new Texture2D(size, size, TextureFormat.RGBA32, false);
        
        for (int y = 0; y < size; y++)
        {
            for (int x = 0; x < size; x++)
            {
                float r = (x < size/2 && y < size/2) ? 1f : 0f;  // Top-left quadrant
                float g = (x >= size/2 && y < size/2) ? 1f : 0f; // Top-right quadrant
                float b = (x < size/2 && y >= size/2) ? 1f : 0f; // Bottom-left quadrant
                float a = (x >= size/2 && y >= size/2) ? 1f : 0f; // Bottom-right quadrant
                
                tex.SetPixel(x, y, new Color(r, g, b, a));
            }
        }
        
        tex.Apply();
        return tex;
    }
    
    private Texture2D CreateTestSplatTexture(int index)
    {
        int size = 64;
        Texture2D tex = new Texture2D(size, size, TextureFormat.RGB24, false);
        
        Color[] colors = {
            Color.red,
            Color.green, 
            Color.blue,
            Color.yellow
        };
        
        Color baseColor = colors[index % colors.Length];
        
        for (int y = 0; y < size; y++)
        {
            for (int x = 0; x < size; x++)
            {
                // Create a simple checkerboard pattern
                bool checker = ((x / 8) + (y / 8)) % 2 == 0;
                Color pixelColor = checker ? baseColor : baseColor * 0.7f;
                tex.SetPixel(x, y, pixelColor);
            }
        }
        
        tex.Apply();
        return tex;
    }
    
    private Texture2D CreateTestTerrainMask()
    {
        int size = 256;
        Texture2D tex = new Texture2D(size, size, TextureFormat.R8, false);
        
        for (int y = 0; y < size; y++)
        {
            for (int x = 0; x < size; x++)
            {
                // Create a gradient from black to white
                float value = (float)x / size;
                tex.SetPixel(x, y, new Color(value, value, value, 1f));
            }
        }
        
        tex.Apply();
        return tex;
    }
}
