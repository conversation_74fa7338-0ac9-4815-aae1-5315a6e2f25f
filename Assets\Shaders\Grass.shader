Shader "Custom/Grass"
{
    Properties
    {
        _MainTex("Grass Texture", 2D) = "white" {}
        _TerrainTex("Mask/Heightmap (Grayscale)", 2D) = "white" {} // Clarified role
        _DarkColor("Dark Color", Color) = (0.2, 0.3, 0.1, 1) // Dark green/brown
        _MidColor("Mid Color", Color) = (0.4, 0.6, 0.2, 1) // Regular green
        _LightColor("Light Color", Color) = (0.7, 0.9, 0.3, 1) // Light green/yellowish
        _ColorGradientOffset("Gradient Offset", Range(-1, 1)) = 0.0 // Adjust where colors map
        _ColorBlend("Overall Color Blend", Range(0, 1)) = 0.8 // How much terrain color affects grass
        _Brightness("Brightness", Range(0.5, 2)) = 1.0
        _Cutoff("Alpha Cutoff", Range(0,1)) = 0.5 // Existing property for cutout
    }

    SubShader
    {
        Tags { "RenderType"="TransparentCutout" "Queue"="AlphaTest" "RenderPipeline" = "UniversalPipeline" }
        Cull Off
        ZWrite On

        Pass
        {
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            // URP shadow keywords for stylized shadows
            #if UNITY_VERSION >= 202120
                #pragma multi_compile _ _MAIN_LIGHT_SHADOWS _MAIN_LIGHT_SHADOWS_CASCADE
            #else
                #pragma multi_compile _ _MAIN_LIGHT_SHADOWS
                #pragma multi_compile _ _MAIN_LIGHT_SHADOWS_CASCADE
            #endif
            #pragma multi_compile_fragment _ _SHADOWS_SOFT

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"

            TEXTURE2D(_MainTex); SAMPLER(sampler_MainTex);
            float4 _MainTex_ST;
            TEXTURE2D(_TerrainTex); SAMPLER(sampler_TerrainTex);
            float4 _TerrainTex_ST;
            float4 _DarkColor;
            float4 _MidColor;
            float4 _LightColor;
            float _ColorGradientOffset;
            float _ColorBlend;
            float _Brightness;
            float _Cutoff;

            StructuredBuffer<float3> _PositionBuffer;
            StructuredBuffer<float> _ScaleBuffer;

            // Terrain properties (set from script)
            float3 _TerrainPosition;
            float3 _TerrainSize;

            struct appdata
            {
                float3 vertex : POSITION;
                float2 uv : TEXCOORD0;
                uint instanceID : SV_InstanceID;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float2 terrainUV : TEXCOORD1;
                float4 pos : SV_POSITION;
                float3 worldPos : TEXCOORD2;
                float3 worldNormal : TEXCOORD3;
                float4 shadowCoord : TEXCOORD4;
            };

            v2f vert(appdata v)
            {
                v2f o;
                uint id = v.instanceID;

                float3 basePos = _PositionBuffer[id];
                float scale = _ScaleBuffer[id];

                // Scale the vertex
                float3 scaledVertex = v.vertex * scale;

                // Billboard calculation - face camera
                float3 cameraPos = _WorldSpaceCameraPos;
                float3 toCameraDir = normalize(cameraPos - basePos);

                // Create billboard rotation (keep Y up, face camera)
                float3 up = float3(0, 1, 0);
                float3 right = normalize(cross(up, toCameraDir));
                float3 forward = cross(right, up);

                // Apply billboard transformation
                float3 billboardVertex = basePos
                + right * scaledVertex.x
                + up * scaledVertex.y
                + forward * scaledVertex.z;

                o.pos = TransformWorldToHClip(billboardVertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);

                // Calculate terrain UV coordinates for this grass position
                float3 relativePos = basePos - _TerrainPosition;
                o.terrainUV = float2(relativePos.x / _TerrainSize.x, relativePos.z / _TerrainSize.z);

                return o;
            }

            float4 frag(v2f i) : SV_Target
            {
                // Sample grass texture
                float4 grassColor = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, i.uv);

                // Sample terrain mask (grayscale value)
                // We only need one channel (e.g., red) as it's grayscale
                float terrainMaskValue = SAMPLE_TEXTURE2D(_TerrainTex, sampler_TerrainTex, i.terrainUV * _TerrainTex_ST.xy + _TerrainTex_ST.zw).r;

                // Apply gradient offset
                terrainMaskValue = saturate(terrainMaskValue + _ColorGradientOffset);

                // Map grayscale value to a color using lerp for a gradient
                float4 terrainMappedColor;
                if (terrainMaskValue < 0.5)
                {
                    // Blend between DarkColor and MidColor for values 0.0 to 0.5
                    terrainMappedColor = lerp(_DarkColor, _MidColor, terrainMaskValue * 2.0);
                }
                else
                {
                    // Blend between MidColor and LightColor for values 0.5 to 1.0
                    terrainMappedColor = lerp(_MidColor, _LightColor, (terrainMaskValue - 0.5) * 2.0);
                }

                // Blend grass color with this newly generated terrain-mapped color
                // Option 1: Tint grass with the terrain-mapped color (multiplication)
                float4 finalColor = lerp(grassColor, grassColor * terrainMappedColor, _ColorBlend);

                // Option 2: Blend the grass color directly with the terrain-mapped color (can be more intense)
                // float4 finalColor = lerp(grassColor, terrainMappedColor, _ColorBlend);


                finalColor.rgb *= _Brightness;

                // Keep original alpha for cutout
                finalColor.a = grassColor.a;

                clip(finalColor.a - _Cutoff); // Use _Cutoff for transparency threshold
                return finalColor;
            }
            ENDHLSL
        }
    }
}